import { GameConfig } from './types';
import Side from './components/Side';
import SplitView from './components/SplitView';

export default function App({ config = {} }: { config?: Partial<GameConfig> }) {
  return (
    <div className="idle-alchemy-container">
      <SplitView
        left={<div className="alchemy-workspace"></div>}
        right={<Side />}
      />

      {/* Toolbar */}
      {/* <div className="alchemy-toolbar">
        <button className="action-btn" type="button">
          Auto Arrange
        </button>
        <div className="action-divider"></div>
        <button className="action-btn" type="button">
          Remove Duplicate
        </button>
        <div className="action-divider"></div>
        <button className="action-btn" type="button">
          Clear
        </button>
      </div> */}
    </div>
  );
}
