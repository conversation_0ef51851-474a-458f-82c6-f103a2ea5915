import {
  RotateCw,
  ZoomOut,
  ZoomIn,
  Moon,
  Search,
  ArrowDownAZ,
  // ArrowDownZA,
  ClockArrowDown,
  // ClockArrowUp,
} from 'lucide-react';
import Element from './Element';

const bases = [
  {
    id: 'water',
    name: 'Water',
    emoji: '💧',
  },
  {
    id: 'fire',
    name: 'Fire',
    emoji: '🔥',
  },
  {
    id: 'earth',
    name: 'Earth',
    emoji: '🌍',
  },
  {
    id: 'air',
    name: 'Air',
    emoji: '🌬️',
  },
];

export default function Side() {
  return (
    <div className="alchemy-sidebar">
      {/* Header */}
      <div className="alchemy-sidebar-header">
        <div className="alchemy-sidebar-controls">
          <button className="icon-btn control-btn" type="button" title="Reset">
            <RotateCw />
          </button>
          <button
            className="icon-btn control-btn"
            type="button"
            title="Zoom Out Canvas"
          >
            <ZoomOut />
          </button>
          <button
            className="icon-btn control-btn"
            type="button"
            title="Zoom In Canvas"
          >
            <ZoomIn />
          </button>
          <button
            className="icon-btn control-btn"
            type="button"
            title="Toggle Dark Mode"
          >
            <Moon />
          </button>
        </div>
        <div className="alchemy-sidebar-title">
          <div className="alchemy-elements-title">Elements (6)</div>
        </div>
        <div className="alchemy-elements-ops">
          <div className="search-input-container">
            <input
              type="text"
              id="alchemy-element-search"
              name="alchemy-element-search"
              placeholder="Search..."
              className="search-input"
            />
            <span className="search-icon">
              <Search />
            </span>
          </div>

          <button
            className="icon-btn sort-btn"
            type="button"
            title="Sort A-Z (currently Z-A)"
          >
            <ArrowDownAZ />
          </button>
          <button
            className="icon-btn sort-btn"
            type="button"
            title="Sort Oldest First (currently Newest First)"
          >
            <ClockArrowDown />
          </button>
        </div>
      </div>

      {/* Element List */}
      <div className="alchemy-elements-container">
        <div className="alchemy-elements-grid">
          {bases.map((base) => (
            <Element key={base.id} {...base} />
          ))}
        </div>
      </div>
    </div>
  );
}
