import { type ComponentChildren } from 'preact';
import { useEffect, useRef, useState } from 'preact/hooks';

const MIN_WIDTH = 75;

interface SplitViewProps {
  left: ComponentChildren;
  right: ComponentChildren;
  className?: string;
}

export default function SplitView({ left, right, className }: SplitViewProps) {
  const [rightWidth, setRightWidth] = useState<undefined | number>();
  const [separatorXPosition, setSeparatorXPosition] = useState<
    undefined | number
  >();
  const [dragging, setDragging] = useState(false);
  const splitViewRef = useRef<HTMLDivElement>(null);
  const rightRef = useRef<HTMLDivElement>(null);

  const onMouseDown = (e: MouseEvent) => {
    console.log('dragging: ', e.clientX);
    setSeparatorXPosition(e.clientX);
    setDragging(true);
  };
  const onTouchStart = (e: TouchEvent) => {
    console.log('touching?: ', e.touches[0].clientX);
    setSeparatorXPosition(e.touches[0].clientX);
    setDragging(true);
  };
  const onMove = (clientX: number) => {
    console.log(dragging, rightWidth, separatorXPosition);
    if (dragging && rightWidth && separatorXPosition) {
      console.log('moving: ', clientX);
      const newRightWidth = rightWidth + clientX - separatorXPosition;
      setSeparatorXPosition(clientX);
      if (newRightWidth < MIN_WIDTH) {
        console.log(1);
        setRightWidth(MIN_WIDTH);
        return;
      }

      if (splitViewRef.current) {
        const splitViewWidth = splitViewRef.current.clientWidth;

        if (newRightWidth > splitViewWidth - MIN_WIDTH) {
          console.log(2);
          setRightWidth(splitViewWidth - MIN_WIDTH);
          return;
        }
      }

      console.log(3);
      setRightWidth(newRightWidth);
    }
  };
  const onMouseMove = (e: MouseEvent) => {
    e.preventDefault();
    onMove(e.clientX);
  };
  const onTouchMove = (e: TouchEvent) => {
    onMove(e.touches[0].clientX);
  };
  const onMouseUp = () => {
    setDragging(false);
  };

  useEffect(() => {
    window.addEventListener('mousemove', onMouseMove);
    window.addEventListener('touchmove', onTouchMove);
    window.addEventListener('mouseup', onMouseUp);
    return () => {
      window.removeEventListener('mousemove', onMouseMove);
      window.removeEventListener('touchmove', onTouchMove);
      window.removeEventListener('mouseup', onMouseUp);
    };
  }, []);

  useEffect(() => {
    if (rightRef.current) {
      if (!rightWidth) {
        setRightWidth(rightRef.current.clientWidth);
        console.log(0, rightRef.current.clientWidth, rightWidth);
        return;
      }
      console.log(1, rightWidth);
      rightRef.current.style.width = `${rightWidth}px`;
    }
  }, [rightRef, rightWidth, setRightWidth]);

  return (
    <div className={`alchemy-split-view ${className ?? ''}`} ref={splitViewRef}>
      <div className="alchemy-left-pane">{left}</div>
      <div
        className="alchemy-split-divider-hitbox"
        onMouseDown={onMouseDown}
        onTouchStart={onTouchStart}
        onTouchEnd={onMouseUp}
      >
        <div className="alchemy-split-divider" />
      </div>
      <div ref={rightRef} className="alchemy-right-pane">
        {right}
      </div>
    </div>
  );
}
