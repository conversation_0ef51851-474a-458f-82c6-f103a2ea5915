.idle-alchemy-container {
  /* width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0; */

  .alchemy-split-view {
    width: 100vw;
    height: 100vh;
    display: flex;
    flex-direction: row;
    align-items: flex-start;
  }

  .alchemy-split-divider-hitbox {
    cursor: col-resize;
    align-self: stretch;
    display: flex;
    align-items: center;
    padding: 0 8px;
  }

  .alchemy-split-divider {
    height: 100%;
    border: 1px solid #e2e8f0;
  }

  .alchemy-left-pane {
    flex: 1;
    /* height: 100%; */
  }

  .alchemy-right-pane {
    /* flex: 1; */
  }

  .alchemy-workspace {
    width: 100%;
    height: 100%;
    overflow: hidden;
    background: #f8f8f8;
  }

  .alchemy-sidebar {
    /* position: absolute;
    top: 0;
    right: 0;
    width: 320px;
    height: 100%; */
    min-width: 320px;
    background: #fff;
    overflow: auto;
    display: flex;
    flex-direction: column;
    border-left: 1px solid #e2e8f0;
    z-index: 1000;
  }

  .alchemy-sidebar-header {
    display: flex;
    flex-direction: column;
    gap: 16px;
    padding: 16px;
    border-bottom: 1px solid #e2e8f0;
    background: #f8fafc;
  }

  .alchemy-sidebar-controls {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
  }

  .icon-btn {
    border-radius: 6px;
    border: 1px solid #cbd5e1;
    background: white;
    color: #475569;
    transition: all 0.15s;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 18px;

    &:hover {
      background: #f1f5f9;
      color: #1e293b;
    }

    svg {
      width: 20px;
      height: 20px;
    }
  }

  .control-btn {
    width: 36px;
    height: 36px;
  }

  .alchemy-sidebar-title {
    text-align: center;
  }

  .alchemy-elements-title {
    font-size: 18px;
    line-height: 1.5;
    font-weight: 600;
    color: #1e293b;
    padding-bottom: 8px;
    border-bottom: solid 1px #cbd5e1;
  }

  .alchemy-elements-ops {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .search-input-container {
    flex: 1;
    position: relative;
  }

  .search-input {
    width: 100%;
    padding: 8px 32px 8px 12px;
    border: 1px solid #cbd5e1;
    border-radius: 6px;
    background: white;
    color: #0f172a;
    font-size: 14px;
    line-height: 1.5;
    outline: none;
    transition: all 0.15s;
  }

  .search-icon {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    color: #94a3b8;
    font-size: 18px;
    pointer-events: none;
    display: flex;
    align-items: center;
    justify-content: center;

    svg {
      width: 20px;
      height: 20px;
    }
  }

  .sort-btn {
    width: 32px;
    height: 32px;
  }

  .alchemy-toolbar {
    position: absolute;
    top: 16px;
    left: 16px;
    z-index: 50;
    background: white;
    border-radius: 6px;
    border: 1px solid #e2e8f0;
    display: flex;
    align-items: center;
    padding: 6px 10px;
  }

  .action-btn {
    padding: 4px 12px;
    font-size: 14px;
    font-weight: 500;
    color: #63748c;
    cursor: pointer;
    transition: color 0.15s;
    background: none;
    border: none;

    &:hover {
      color: #1e293b;
    }
  }

  .action-divider {
    width: 1px;
    height: 16px;
    background: #cbd5e1;
    margin: 0 8px;
  }

  .alchemy-elements-container {
    flex: 1;
    overflow-y: auto;
  }

  .alchemy-elements-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    padding: 8px;
  }

  .alchemy-element {
    display: flex;
    align-items: center;
    padding: 6px 10px;
    cursor: pointer;
    transition: all 0.15s;
    border-radius: 4px;
    gap: 8px;
    font-family: system-ui, -apple-system, sans-serif;
    width: fit-content;
    min-width: fit-content;
    border: 1px solid #e2e8f0;
    background: white;

    &:hover {
      background: #f1f5f9;
      border-color: #cbd5e1;
      transform: translateY(-1px);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }
  }

  .alchemy-element-emoji {
    font-size: 18px;
    line-height: 1;
    flex-shrink: 0;
  }

  .alchemy-element-name {
    font-size: 14px;
    font-weight: 500;
    color: #334155;
    font-family: system-ui, -apple-system, sans-serif;
  }
}
