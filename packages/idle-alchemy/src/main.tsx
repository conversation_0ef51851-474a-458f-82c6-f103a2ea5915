import { render } from 'preact';
import './index.css';
import App from './App';
// import Usage from './components/Usage.tsx';
import type { GameConfig } from './types';

if (!__IS_PUBLISH__) {
  render(
    <>
      {/* <Usage /> */}
      <App />
    </>,
    document.getElementById('app')!
  );
}

const Alchemy = {
  async init(selector: Element | string, config: Partial<GameConfig>) {
    const el =
      typeof selector === 'string'
        ? document.querySelector(selector)
        : selector;
    if (!el) {
      console.warn('Cannot find the root element');
      const notice = document.createElement('div');
      notice.style.textAlign = 'center';
      notice.style.padding = '20px';
      notice.innerHTML = 'Cannot find the root element';
      document.body.append(notice);
      return;
    }

    let { root = location.origin } = config;
    if (root.at(-1) !== '/') {
      root += '/';
    }

    // default to show a loading indicator
    const loading = document.createElement('div');
    loading.innerHTML = `
      <div id="loading-screen" class="loading-screen">
        <div class="loading-content">
          <div class="loading-spinner"></div>
          <h2 style="margin: 0 0 8px; color: #1e293b">
            🧪 Loading Idle Alchemy...
          </h2>
          <p style="margin: 0; color: #64748b; font-size: 14px">
            Preparing elements...
          </p>
        </div>
      </div>
    `;
    const style = document.createElement('style');
    style.innerHTML = `
    .loading-screen {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: system-ui, -apple-system, sans-serif;
        z-index: 9999;
      }
      .dark .loading-screen {
        background: linear-gradient(135deg, #0f172a, #1e293b);
      }
      .loading-content {
        text-align: center;
      }
      .loading-spinner {
        width: 40px;
        height: 40px;
        margin: 0 auto 20px;
        border: 3px solid #e2e8f0;
        border-top: 3px solid #3b82f6;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }
    `;
    document.head.appendChild(style);
    el.append(loading);

    const loadCSS = async (url: string) => {
      // load google font
      // const googleFont = document.createElement('link');
      // googleFont.rel = 'stylesheet';
      // googleFont.href =
      //   'https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100..900;1,100..900&display=swap';
      // document.head.appendChild(googleFont);

      return new Promise((resolve, reject) => {
        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.type = 'text/css';
        link.href = url;
        link.onload = resolve;
        link.onerror = reject;
        document.head.appendChild(link);
      });
    };

    await loadCSS(root + 'alchemy.css');
    el.innerHTML = '';
    render(<App config={config} />, el);
  },
};

export default Alchemy;
